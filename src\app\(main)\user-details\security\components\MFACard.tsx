"use client";

import {
  Card,
  CardContent,
  CardDescription,
  Card<PERSON>eader,
  CardTitle,
} from "@/components/ui/card";
import {
  InputOTP,
  InputOTPGroup,
  InputOTPSeparator,
  InputOTPSlot,
} from "@/components/ui/input-otp";
import React, { useEffect, useState } from "react";
import { Copy, Trash2 } from "lucide-react";
import { Button } from "@/components/ui/button";
import { useGetSoftwareToken } from "@/queries/get-software-token";
import { toast } from "@/hooks/use-toast";
import { useVerifySoftwareToken } from "@/queries/verify-software-token";
import { useSetMFAPreference, useSetMFAPreferenceUpdate } from "@/queries";
import { useIsMFA } from "@/queries/is-mfa";
import ToastContent from "@/components/mf/ToastContent";
import QRCode from "react-qr-code";
import { Input } from "@/components/ui/input";
import { Progress } from "@/components/ui/progress";

export default function SecuritySettings() {
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 gap-6 p-4">
      <MFACard />
      <PasswordResetCard />
    </div>
  );
}

/* ---------------------- MFA Card --------------------- */
export const MFACard = () => {
  const IsMFA = useIsMFA();
  const [Enable, setEnable] = useState(false);
  const [toastData, setToastData] = useState<any>(null);

  const onSetMFAPreferenceError = console.warn;
  const onSetMFAPreferenceSuccess = (d: any) => {
    if (d?.data?.message) {
      setToastData({
        type: "success",
        title: "MFA Preference Updated",
        description: d.data.message,
      });
    }
    IsMFA.refetch();
  };

  const SetMFAPreference = useSetMFAPreference(
    onSetMFAPreferenceError,
    onSetMFAPreferenceSuccess
  );

  const SetMFAPreferenceUpdate = useSetMFAPreferenceUpdate(
    console.warn,
    (d: any) => {
      if (d?.data?.message) {
        setToastData({
          type: "success",
          title: "MFA Disabled",
          description: d.data.message,
        });
      }
      setEnable(false);
      IsMFA.refetch();
    }
  );

  const disableMFA = () => {
    const body = {
      access_token:
        typeof window !== "undefined" ? localStorage.getItem("AccessToken") : "",
      enable_software_token_mfa: false,
    };
    SetMFAPreferenceUpdate.mutate({ body });
  };

  useEffect(() => {
    if (IsMFA.data?.enabled_streams) setEnable(true);
  }, [IsMFA.data]);

  return (
    <Card className="shadow-md border">
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle>Multi-Factor Authentication (MFA)</CardTitle>
            <CardDescription>
              Add an extra layer of security using an Authenticator App.
            </CardDescription>
          </div>
          {Enable && (
            <Button
              variant="ghost"
              size="sm"
              title="Disable MFA"
              onClick={disableMFA}
              className="text-red-600 hover:bg-red-50"
            >
              <Trash2 className="h-4 w-4" />
            </Button>
          )}
        </div>
      </CardHeader>
      <CardContent>
        <AuthenticatorSetup
          isEnabled={IsMFA.data?.enabled_streams?.includes(
            "SOFTWARE_TOKEN_MFA"
          )}
          onRefetch={IsMFA.refetch}
          setToastData={setToastData}
        />
        {toastData && (
          <ToastContent
            type={toastData.type}
            title={toastData.title}
            description={toastData.description}
          />
        )}
      </CardContent>
    </Card>
  );
};

function AuthenticatorSetup({
  isEnabled,
  onRefetch,
  setToastData,
}: {
  isEnabled: boolean;
  onRefetch: () => void;
  setToastData: (data: any) => void;
}) {
  const [OTP, setOTP] = useState("");
  const token = useGetSoftwareToken(!isEnabled, console.warn, console.debug);
  const VerifySoftwareToken = useVerifySoftwareToken(console.debug);

  const handleVerify = () => {
    if (OTP.length < 6) {
      toast({ title: "Enter OTP", variant: "destructive" });
      return;
    }
    VerifySoftwareToken.mutate({
      body: {
        access_token: localStorage.getItem("AccessToken") ?? "",
        user_code: OTP,
      },
    });
  };

  const QRGerator = (secret_code: string) => {
    if (!secret_code) return "";
    const username = localStorage.getItem("username") ?? "";
    return `otpauth://totp/${username}?secret=${secret_code}&issuer=App&algorithm=SHA1&digits=6&period=30`;
  };

  if (isEnabled) {
    return (
      <div className="p-3 bg-green-50 border border-green-200 rounded-lg">
        <p className="text-green-700 text-sm font-medium">
          ✅ MFA is enabled on your account
        </p>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <p className="text-sm font-medium text-gray-600">
        Step 1: Scan or copy token
      </p>
      <div className="flex items-center justify-between rounded-md border p-3">
        <span className="font-mono text-sm">
          {token.data?.secret_code ?? "Loading..."}
        </span>
        {token.data?.secret_code && (
          <Button
            variant="ghost"
            size="sm"
            onClick={() => {
              navigator.clipboard.writeText(token.data?.secret_code ?? "");
              toast({ title: "Copied to clipboard" });
            }}
          >
            <Copy className="h-4 w-4" />
          </Button>
        )}
      </div>
      {QRGerator(token.data?.secret_code) && (
        <QRCode
          size={150}
          value={QRGerator(token.data?.secret_code) ?? ""}
          className="mx-auto"
        />
      )}

      <p className="text-sm font-medium text-gray-600">Step 2: Enter code</p>
      <div className="flex justify-center">
        <InputOTP maxLength={6} onChange={setOTP}>
          <InputOTPGroup className="gap-2">
            <InputOTPSlot index={0} />
            <InputOTPSlot index={1} />
            <InputOTPSlot index={2} />
          </InputOTPGroup>
          <InputOTPSeparator />
          <InputOTPGroup className="gap-2">
            <InputOTPSlot index={3} />
            <InputOTPSlot index={4} />
            <InputOTPSlot index={5} />
          </InputOTPGroup>
        </InputOTP>
      </div>
      <Button className="w-full" onClick={handleVerify}>
        Verify & Enable MFA
      </Button>
    </div>
  );
}

/* ---------------------- Password Reset --------------------- */
function PasswordResetCard() {
  const [password, setPassword] = useState("");
  const [strength, setStrength] = useState(0);

  useEffect(() => {
    // simple strength meter
    let score = 0;
    if (password.length > 5) score++;
    if (/[A-Z]/.test(password)) score++;
    if (/[0-9]/.test(password)) score++;
    if (/[^A-Za-z0-9]/.test(password)) score++;
    setStrength((score / 4) * 100);
  }, [password]);

  return (
    <Card className="shadow-md border">
      <CardHeader>
        <CardTitle>Reset Your Password</CardTitle>
        <CardDescription>
          Ensure your password is strong and secure.
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <Input type="password" placeholder="Current Password" />
        <div>
          <Input
            type="password"
            placeholder="New Password"
            value={password}
            onChange={(e) => setPassword(e.target.value)}
          />
          <Progress value={strength} className="h-2 mt-2" />
          <p className="text-xs mt-1">
            {strength < 40
              ? "Weak"
              : strength < 70
              ? "Medium"
              : "Strong"}
          </p>
        </div>
        <Input type="password" placeholder="Confirm Password" />
        <Button className="w-full">Update Password</Button>
      </CardContent>
    </Card>
  );
}
